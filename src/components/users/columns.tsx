"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { UserWithRole } from "better-auth/plugins/admin";
import { format } from "date-fns";
import {
  ArrowUpDown,
  BanIcon,
  CheckCircleIcon,
  EditIcon,
  EyeIcon,
  MoreHorizontalIcon,
  ShieldIcon,
  UserIcon,
  XCircleIcon,
} from "lucide-react";
import Link from "next/link";
import type { User } from "@/lib/auth";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";

// Role color mapping
const getRoleColor = (role: string) => {
  switch (role) {
    case "admin":
      return "destructive";
    case "contractor":
      return "tc_blue";
    case "homeowner":
      return "tc_orange";
    default:
      return "secondary";
  }
};

// Status indicators component
function StatusIndicators({ user }: { user: User }) {
  return (
    <div className="flex items-center gap-2">
      <TooltipProvider>
        {user.emailVerified ? (
          <Tooltip>
            <TooltipTrigger>
              <CheckCircleIcon className="h-4 w-4 text-green-600" />
            </TooltipTrigger>
            <TooltipContent>Email Verified</TooltipContent>
          </Tooltip>
        ) : (
          <Tooltip>
            <TooltipTrigger>
              <XCircleIcon className="h-4 w-4 text-red-600" />
            </TooltipTrigger>
            <TooltipContent>Email Not Verified</TooltipContent>
          </Tooltip>
        )}

        {user.twoFactorEnabled && (
          <Tooltip>
            <TooltipTrigger>
              <ShieldIcon className="h-4 w-4 text-blue-600" />
            </TooltipTrigger>
            <TooltipContent>2FA Enabled</TooltipContent>
          </Tooltip>
        )}

        {user.banned && (
          <Tooltip>
            <TooltipTrigger>
              <BanIcon className="h-4 w-4 text-red-600" />
            </TooltipTrigger>
            <TooltipContent>Banned User</TooltipContent>
          </Tooltip>
        )}
      </TooltipProvider>
    </div>
  );
}

// Actions dropdown component
function UserActionsDropdown({ user }: { user: User }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontalIcon className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem asChild>
          <Link href={`/admin/users/${user.id}`}>
            <EyeIcon className="mr-2 h-4 w-4" />
            View Details
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/admin/users/${user.id}/edit`}>
            <EditIcon className="mr-2 h-4 w-4" />
            Edit User
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <UserIcon className="mr-2 h-4 w-4" />
          Impersonate
        </DropdownMenuItem>
        <DropdownMenuItem
          className={user.banned ? "text-green-600" : "text-red-600"}
        >
          <BanIcon className="mr-2 h-4 w-4" />
          {user.banned ? "Unban User" : "Ban User"}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const columns: ColumnDef<UserWithRole | undefined>[] = [
  {
    accessorKey: "name",
    header: "User",
    cell: ({ row }) => {
      const user = row.original;
      if (!user) return null;

      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={user.image as string} />
            <AvatarFallback>
              {user.name?.charAt(0)?.toUpperCase() || "U"}
            </AvatarFallback>
          </Avatar>
          <div className="space-y-1">
            <p className="font-medium text-sm leading-none">{user.name}</p>
            <p className="text-muted-foreground text-sm">{user.email}</p>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "role",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Role
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const user = row.original;
      if (!user?.role) return null;

      return (
        <Badge
          variant={
            getRoleColor(user.role) as
              | "default"
              | "secondary"
              | "destructive"
              | "outline"
              | "tc_orange"
              | "tc_blue"
          }
        >
          {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const user = row.original as User;
      if (!user) return null;

      return <StatusIndicators user={user} />;
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Joined
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const user = row.original;
      if (!user?.createdAt) return null;

      return (
        <div className="text-sm">
          {format(new Date(user.createdAt), "MMM d, yyyy")}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const user = row.original as User;
      if (!user) return null;

      return <UserActionsDropdown user={user} />;
    },
  },
];
