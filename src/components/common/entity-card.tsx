import Image from "next/image";
import Link from "next/link";
import { type ReactNode, useId } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { cn } from "@/lib/utils";

export interface EntityCardAction {
  label: string;
  href?: string;
  onClick?: () => void;
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link"
    | "tc_blue"
    | "tc_orange";
  size?: "default" | "sm" | "lg" | "icon";
  disabled?: boolean;
  icon?: ReactNode;
}

export interface EntityCardBadge {
  label: string;
  variant?: "default" | "secondary" | "destructive" | "outline";
  className?: string;
}

export interface EntityCardMetaItem {
  icon: ReactNode;
  label: string;
  value: string | ReactNode;
  className?: string;
}

export interface EntityCardProps {
  // Core content
  title: string;
  subtitle?: string;
  description?: string;

  // Visual elements
  imageUrl?: string;
  imageAlt?: string;
  badges?: EntityCardBadge[];

  // Metadata
  metadata?: EntityCardMetaItem[];

  // Actions
  actions?: EntityCardAction[];
  primaryAction?: EntityCardAction;

  // Layout options
  variant?: "default" | "compact" | "detailed";
  orientation?: "vertical" | "horizontal";

  // Styling
  className?: string;
  hoverable?: boolean;

  // Custom content
  children?: ReactNode;
  headerExtra?: ReactNode;
  footerExtra?: ReactNode;
}

export function EntityCard({
  title,
  subtitle,
  description,
  imageUrl,
  imageAlt,
  badges = [],
  metadata = [],
  actions = [],
  primaryAction,
  variant = "default",
  orientation = "vertical",
  className,
  hoverable = true,
  children,
  headerExtra,
  footerExtra,
}: EntityCardProps) {
  const isHorizontal = orientation === "horizontal";
  const isCompact = variant === "compact";
  const isDetailed = variant === "detailed";
  const id = useId();

  const cardContent = (
    <Card
      className={cn(
        "group relative flex h-full flex-col overflow-hidden transition-all duration-300",
        hoverable && "hover:shadow-md",
        isHorizontal && "flex-row",
        className,
      )}
    >
      {/* Image Section - Mobile-optimized */}
      {imageUrl && (
        <div
          className={cn(
            "relative overflow-hidden",
            isHorizontal
              ? "w-32 flex-shrink-0 sm:w-48" // Smaller on mobile, larger on desktop
              : "h-40 w-full sm:h-48", // Shorter on mobile, taller on desktop
            isCompact && (isHorizontal ? "w-24 sm:w-32" : "h-32 sm:h-36"),
            !isCompact && !isHorizontal && "-mt-6",
          )}
        >
          <Image
            src={imageUrl}
            alt={imageAlt || title}
            fill
            className="object-cover transition-transform duration-500 group-hover:scale-105"
            priority={false}
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
          />
          {hoverable && (
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
          )}
        </div>
      )}

      {/* Content Section */}
      <div
        className={cn(
          "flex flex-1 flex-col",
          isHorizontal && "p-4",
          !isHorizontal && imageUrl && "p-4",
        )}
      >
        {/* Header */}
        <CardHeader
          className={cn(
            "flex-shrink-0",
            isCompact ? "pb-2" : "pb-3",
            (isHorizontal || !imageUrl) && "pt-0",
          )}
        >
          <div className="flex items-start justify-between gap-2">
            <div className="min-w-0 flex-1">
              <CardTitle
                className={cn(
                  "line-clamp-2",
                  isCompact ? "text-base" : "text-lg",
                )}
              >
                {title}
              </CardTitle>
              {subtitle && (
                <p className="mt-1 line-clamp-1 text-muted-foreground text-sm">
                  {subtitle}
                </p>
              )}
            </div>

            {/* Badges */}
            {badges.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {badges.map((badge) => (
                  <Badge
                    key={`badge-${id}-${badge.label}`}
                    variant={badge.variant}
                    className={badge.className}
                  >
                    {badge.label}
                  </Badge>
                ))}
              </div>
            )}

            {headerExtra}
          </div>
        </CardHeader>

        {/* Content */}
        <CardContent className={cn("flex-1", isCompact ? "py-2" : "py-3")}>
          {description && (
            <p
              className={cn(
                "mb-3 text-muted-foreground text-sm",
                isCompact ? "line-clamp-2" : "line-clamp-3",
              )}
            >
              {description}
            </p>
          )}

          {/* Metadata */}
          {metadata.length > 0 && (
            <div
              className={cn(
                "space-y-2",
                isDetailed && "grid grid-cols-2 gap-2 space-y-0",
              )}
            >
              {metadata.map((item) => (
                <div
                  key={`metadata-${id}-${item.label}`}
                  className={cn(
                    "flex items-center gap-2 text-sm",
                    item.className,
                  )}
                >
                  <span className="flex-shrink-0 text-tradecrews-orange">
                    {item.icon}
                  </span>
                  <span className="flex-shrink-0 text-muted-foreground">
                    {item.label}:
                  </span>
                  <span className="truncate font-medium">{item.value}</span>
                </div>
              ))}
            </div>
          )}

          {children}
        </CardContent>

        {/* Footer - Mobile-first responsive */}
        {(actions.length > 0 || primaryAction || footerExtra) && (
          <CardFooter
            className={cn(
              "flex-shrink-0 gap-2",
              isCompact ? "pt-2" : "pt-3",
              // Mobile: always stack vertically, Desktop: horizontal if <= 2 actions
              "flex-col space-y-2 sm:flex-row sm:justify-between sm:space-y-0",
              actions.length <= 1 && !primaryAction && "sm:flex-row",
            )}
          >
            <div className="flex flex-col gap-2 sm:flex-row sm:flex-wrap">
              {primaryAction && (
                <ActionButton action={primaryAction} isPrimary />
              )}
              {actions.map((action) => (
                <ActionButton
                  key={`action-${id}-${action.label}`}
                  action={action}
                />
              ))}
            </div>
            {footerExtra}
          </CardFooter>
        )}
      </div>
    </Card>
  );

  // Wrap in link if primary action has href
  if (primaryAction?.href && !primaryAction.onClick) {
    return (
      <Link href={primaryAction.href} className="block h-full">
        {cardContent}
      </Link>
    );
  }

  return cardContent;
}

function ActionButton({
  action,
  isPrimary = false,
}: {
  action: EntityCardAction;
  isPrimary?: boolean;
}) {
  const buttonProps = {
    variant: action.variant || (isPrimary ? "tc_blue" : "outline"),
    size: action.size || "sm",
    disabled: action.disabled,
    onClick: action.onClick,
    className: cn(
      // Mobile: full width buttons, Desktop: auto width
      "w-full sm:w-auto",
      isPrimary && "sm:flex-1",
      action.disabled && "cursor-not-allowed opacity-50",
      // Better touch targets on mobile
      "min-h-[44px] sm:min-h-[36px]",
    ),
  };

  const content = (
    <>
      {action.icon && <span className="mr-2 flex-shrink-0">{action.icon}</span>}
      <span className="truncate">{action.label}</span>
    </>
  );

  if (action.href && !action.onClick) {
    return (
      <Button asChild {...buttonProps}>
        <Link href={action.href}>{content}</Link>
      </Button>
    );
  }

  return <Button {...buttonProps}>{content}</Button>;
}
