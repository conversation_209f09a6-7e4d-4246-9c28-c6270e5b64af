"use client";

import { useMutation, useQuery } from "@tanstack/react-query";
import { PlusIcon, SearchIcon, XIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { useOrganization } from "@/components/contexts/organization-context";
import { useTRPC } from "@/components/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import type { Organization } from "@/db/schema";

export function CrewSettings() {
  const trpc = useTRPC();
  const { organization } = useOrganization();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<Organization[]>([]);
  const organizationId = organization?.id;

  const { data: crewMembers, refetch } = useQuery(
    trpc.contractor.getCrewMembers.queryOptions(
      { organizationId: organizationId || "" },
      { enabled: !!organizationId },
    ),
  );

  const addCrewMemberMutation = useMutation(
    trpc.contractor.addCrewMember.mutationOptions({
      onSuccess: () => {
        refetch();
        toast.success("Crew member added successfully");
        setSearchQuery("");
        setSearchResults([]);
      },
    }),
  );

  const removeCrewMemberMutation = useMutation(
    trpc.contractor.removeCrewMember.mutationOptions({
      onSuccess: () => {
        refetch();
        toast.success("Crew member removed successfully");
      },
    }),
  );

  const searchMutation = useMutation(
    trpc.contractor.search.mutationOptions({
      onSuccess: (data) => {
        setSearchResults(data);
      },
    }),
  );

  if (!organizationId) {
    return null;
  }

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      searchMutation.mutate({
        query: searchQuery,
        excludeIds: [organizationId || ""],
      });
    } catch {
      toast.error("Failed to search organizations");
    }
  };

  const handleAddCrewMember = (crewMemberId: string) => {
    addCrewMemberMutation.mutate({ organizationId, crewMemberId });
  };

  const handleRemoveCrewMember = (crewMemberId: string) => {
    removeCrewMemberMutation.mutate({ organizationId, crewMemberId });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Add Crew Members</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4 flex gap-2">
            <Input
              placeholder="Search for contractors..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
            <Button onClick={handleSearch} variant="outline">
              <SearchIcon className="mr-2 h-4 w-4" />
              Search
            </Button>
          </div>

          {searchResults.length > 0 && (
            <div className="mt-4 space-y-2">
              <h3 className="font-medium text-sm">Search Results</h3>
              <div className="divide-y">
                {searchResults.map((org) => (
                  <div
                    key={org.id}
                    className="flex items-center justify-between py-2"
                  >
                    <div>
                      <p className="font-medium">{org.name}</p>
                      {org.trade && (
                        <Badge variant="outline" className="mt-1">
                          {org.trade.name}
                        </Badge>
                      )}
                    </div>
                    <Button
                      size="sm"
                      onClick={() => handleAddCrewMember(org.id)}
                      disabled={addCrewMemberMutation.isPending}
                    >
                      <PlusIcon className="mr-2 h-4 w-4" />
                      Add
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Current Crew Members</CardTitle>
        </CardHeader>
        <CardContent>
          {!crewMembers || crewMembers.length === 0 ? (
            <p className="text-muted-foreground">No crew members added yet.</p>
          ) : (
            <div className="divide-y">
              {crewMembers.map((crew) => (
                <div
                  key={crew.id}
                  className="flex items-center justify-between py-3"
                >
                  <div>
                    <p className="font-medium">{crew.crewMember.name}</p>
                    {crew.crewMember.trade && (
                      <Badge variant="outline" className="mt-1">
                        {crew.crewMember.trade.name}
                      </Badge>
                    )}
                  </div>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleRemoveCrewMember(crew.crewMember.id)}
                    disabled={removeCrewMemberMutation.isPending}
                  >
                    <XIcon className="mr-2 h-4 w-4" />
                    Remove
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
