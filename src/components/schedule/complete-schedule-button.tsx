import { useMutation } from "@tanstack/react-query";
import { CheckCircle, Loader2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface CompleteScheduleButtonProps {
  scheduleId: string;
  jobId: string;
  isRecurring: boolean;
  onComplete?: () => void;
}

export function CompleteScheduleButton({
  scheduleId,
  jobId,
  isRecurring,
  onComplete,
}: CompleteScheduleButtonProps) {
  const trpc = useTRPC();
  const [dialogOpen, setDialogOpen] = useState(false);

  const markComplete = useMutation(
    trpc.schedules.markComplete.mutationOptions({
      onSuccess: () => {
        toast.success(
          isRecurring
            ? "Schedule marked as complete. Payment will be processed."
            : "Job marked as complete. Payment will be processed.",
        );
        setDialogOpen(false);
        if (onComplete) onComplete();
      },
      onError: (error) => {
        toast.error(`Error: ${error.message}`);
      },
    }),
  );

  return (
    <>
      <Button
        onClick={() => setDialogOpen(true)}
        className="bg-green-600 hover:bg-green-700"
      >
        <CheckCircle className="mr-2 h-4 w-4" />
        Mark Complete
      </Button>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Mark as Complete</DialogTitle>
            <DialogDescription>
              {isRecurring
                ? "This will mark this scheduled instance as complete and process payment to the contractor."
                : "This will mark the job as complete and process payment to the contractor."}
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDialogOpen(false)}
              disabled={markComplete.isPending}
            >
              Cancel
            </Button>
            <Button
              onClick={() => markComplete.mutate({ id: scheduleId })}
              disabled={markComplete.isPending}
              className="bg-green-600 hover:bg-green-700"
            >
              {markComplete.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Confirm"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
