import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { Building, Clock, DollarSign, MapPin, Star } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { BID_STATUS_VARIANTS, getStatusVariant } from "@/lib/utils";
import { useTRPC } from "../trpc/client";

interface BidCardProps {
  bidId: string;
  showJobDetails?: boolean;
  showAcceptButton?: boolean;
}

export function BidCard({
  bidId,
  showJobDetails = false,
  showAcceptButton = false,
}: BidCardProps) {
  const trpc = useTRPC();
  const { data: bid } = useQuery(trpc.bids.getById.queryOptions({ id: bidId }));

  // Fetch reviews for this organization
  const { data: reviews } = useQuery(
    trpc.reviews.listForOrganization.queryOptions(
      {
        organizationId: bid?.organization.id || "",
      },
      { enabled: !!bid?.organization.id },
    ),
  );

  // Calculate average rating
  const averageRating =
    reviews && reviews.length > 0
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
      : 0;

  if (!bid) {
    return null;
  }

  return (
    <Card className="BidCard">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="font-medium text-base">{bid.name}</CardTitle>
          <Badge variant={getStatusVariant(bid.status, BID_STATUS_VARIANTS)}>
            {bid.status}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="pb-2">
        {showJobDetails && (
          <div className="mb-2 text-muted-foreground text-sm">
            <p>Project: {bid.job.name}</p>
            <p>Property: {bid.job.property.name}</p>
          </div>
        )}

        <div className="flex flex-wrap items-center gap-x-4 gap-y-1 text-sm">
          <div className="flex items-center text-muted-foreground">
            <DollarSign className="mr-1 h-3.5 w-3.5" />${bid.amount}
          </div>

          <div className="flex items-center text-muted-foreground">
            <Clock className="mr-1 h-3.5 w-3.5" />
            {bid.estimatedDuration} days
          </div>

          <div className="flex items-center text-muted-foreground">
            <Link
              href={`/contractors/${bid.organization.id}`}
              className="flex text-sm text-tradecrews-blue hover:underline"
            >
              <Building className="mr-1 h-3.5 w-3.5" />
              {bid.organization.name}
            </Link>
          </div>

          {typeof bid.distance === "number" && (
            <div className="flex items-center text-muted-foreground">
              <MapPin className="mr-1 h-3.5 w-3.5" />
              {Number(bid.distance).toFixed(1)} miles away
            </div>
          )}
        </div>

        <div className="mt-2 flex items-center text-sm">
          <div className="flex items-center">
            {[1, 2, 3, 4, 5].map((star) => (
              <Star
                key={star}
                className={`h-3.5 w-3.5 ${
                  star <= Math.round(averageRating)
                    ? "fill-yellow-400 text-yellow-400"
                    : "text-gray-300"
                }`}
              />
            ))}
          </div>
          {reviews && reviews.length > 0 ? (
            <span className="ml-1 text-muted-foreground">
              {averageRating.toFixed(1)} ({reviews.length}{" "}
              {reviews.length === 1 ? "review" : "reviews"})
            </span>
          ) : (
            <span className="ml-1 text-muted-foreground">No reviews yet</span>
          )}
        </div>

        <div className="mt-2 text-muted-foreground text-sm">
          <p>Submitted: {format(bid.createdAt, "PPP")}</p>
        </div>
      </CardContent>

      <CardFooter className="flex gap-2">
        <Button size="sm" asChild>
          <Link href={`/bids/${bid.id}`}>View Details</Link>
        </Button>

        {showAcceptButton && bid.status === "PROPOSED" && (
          <Button
            size="sm"
            variant="default"
            className="bg-green-600 hover:bg-green-700"
            asChild
          >
            <Link href={`/bids/${bid.id}/accept`}>Accept Bid</Link>
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
