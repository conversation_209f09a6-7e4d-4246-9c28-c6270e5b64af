import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

interface NewBidNotificationProps {
  bidName: string;
  bidId: string;
  jobName: string;
  contractorName: string;
  amount: number;
  estimatedDuration: number;
  userId: string;
  recipientEmail: string;
}

const NewBidNotificationEmail = ({
  bidName,
  bidId,
  jobName,
  contractorName,
  amount,
  estimatedDuration,
  userId,
  recipientEmail,
}: NewBidNotificationProps) => {
  return (
    <Html>
      <Head />
      <Preview>New bid received for your project: {jobName}</Preview>
      <Tailwind>
        <Body className="bg-gray-100 py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[8px] bg-white p-[24px]">
            <Section>
              <Heading className="mt-[10px] mb-[24px] text-center font-bold text-[28px] text-gray-800">
                New Bid Received
              </Heading>

              <Text className="mb-[16px] text-center font-medium text-[18px] text-gray-700">
                A contractor has submitted a bid for your project
              </Text>

              <Section className="mb-[24px] rounded-[8px] bg-blue-50 p-[20px]">
                <Text className="mb-[8px] font-medium text-[18px] text-gray-700">
                  {bidName}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Project:</strong> {jobName}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Contractor:</strong> {contractorName}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Bid Amount:</strong> ${amount}
                </Text>
                <Text className="text-[16px] text-gray-600">
                  <strong>Estimated Duration:</strong> {estimatedDuration} days
                </Text>
              </Section>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                Review this bid and compare it with others to find the best
                contractor for your project.
              </Text>

              <Section className="mb-[24px] text-center">
                <Button
                  className="box-border rounded-[4px] bg-blue-600 px-[32px] py-[14px] text-center font-bold text-[16px] text-white no-underline"
                  href={`https://tradecrews.com/bids/${bidId}`}
                >
                  View Bid Details
                </Button>
              </Section>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                You can accept this bid or wait for more contractors to respond
                to your project.
              </Text>
            </Section>

            <Section className="border-gray-200 border-t pt-[24px] text-center">
              <Text className="m-0 text-center text-[12px] text-gray-500">
                © {new Date().getFullYear()} TradeCrews. All rights reserved.
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                <a
                  href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                  className="text-gray-500 underline"
                >
                  Unsubscribe
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default NewBidNotificationEmail;
