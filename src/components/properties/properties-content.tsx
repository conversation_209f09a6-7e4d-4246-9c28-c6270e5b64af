"use client";

import { useQuery } from "@tanstack/react-query";
import { PropertyCard } from "@/components/properties/property-card";
import { useTRPC } from "@/components/trpc/client";
import { CardGrid } from "@/components/ui/responsive-grid";

export function PropertiesContent() {
  const trpc = useTRPC();
  const { data: properties } = useQuery(trpc.properties.list.queryOptions());

  if (!properties || properties.length === 0) {
    return (
      <div className="flex min-h-[200px] items-center justify-center text-center">
        <div className="space-y-2">
          <p className="text-muted-foreground">
            No properties found. Create your first property to get started.
          </p>
        </div>
      </div>
    );
  }

  return (
    <CardGrid>
      {properties.map((property) => (
        <PropertyCard key={property.id} property={property} />
      ))}
    </CardGrid>
  );
}
