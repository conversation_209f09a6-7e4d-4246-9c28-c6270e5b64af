/** biome-ignore-all lint/suspicious/noExplicitAny: Type of options is unknown */
import "server-only";

import { dehydrate, HydrationBoundary } from "@tanstack/react-query";
import {
  createTRPCOptionsProxy,
  type TRPCQueryOptions,
} from "@trpc/tanstack-react-query";
import { cache } from "react";
import { appRouter } from "@/lib/trpc";
import { createContext } from "@/lib/trpc/core/context";
import { makeQueryClient } from "@/lib/trpc/core/query-client";

export const getQueryClient = cache(makeQueryClient);

export const trpc = createTRPCOptionsProxy({
  ctx: createContext,
  router: appRouter,
  queryClient: getQueryClient,
});

export function prefetch<T extends ReturnType<TRPCQueryOptions<any>>>(
  queryOptions: T,
) {
  const queryClient = getQueryClient();

  if (queryOptions.queryKey[1]?.type === "infinite") {
    void queryClient.prefetchInfiniteQuery(
      queryOptions as Parameters<typeof queryClient.prefetchInfiniteQuery>[0],
    );
  } else {
    void queryClient.prefetchQuery(queryOptions);
  }
}

/**
 * Prefetch multiple queries in parallel
 */
export async function prefetchParallel(
  queryOptions: Array<ReturnType<TRPCQueryOptions<any>>>,
) {
  const queryClient = getQueryClient();

  const prefetchPromises = queryOptions.map((options) => {
    if (options.queryKey[1]?.type === "infinite") {
      return queryClient.prefetchInfiniteQuery({
        ...options,
        initialPageParam: undefined,
      } as Parameters<typeof queryClient.prefetchInfiniteQuery>[0]);
    }
    return queryClient.prefetchQuery(options);
  });

  await Promise.allSettled(prefetchPromises);
}

/**
 * Prefetch with error handling and fallbacks
 */
export async function prefetchWithFallback(
  primaryOptions: ReturnType<TRPCQueryOptions<any>>,
  fallbackOptions?: ReturnType<TRPCQueryOptions<any>>,
) {
  const queryClient = getQueryClient();

  try {
    await queryClient.prefetchQuery(primaryOptions);
  } catch (error) {
    console.warn("Primary prefetch failed, trying fallback:", error);

    if (fallbackOptions) {
      try {
        await queryClient.prefetchQuery(fallbackOptions);
      } catch (fallbackError) {
        console.error("Fallback prefetch also failed:", fallbackError);
      }
    }
  }
}

/**
 * Conditional prefetch based on user context
 */
export async function prefetchConditional(
  conditions: Array<{
    condition: boolean;
    queryOptions: ReturnType<TRPCQueryOptions<any>>;
  }>,
) {
  const queryClient = getQueryClient();

  const validQueries = conditions
    .filter(({ condition }) => condition)
    .map(({ queryOptions }) => queryOptions);

  if (validQueries.length === 0) return;

  const prefetchPromises = validQueries.map((options) => {
    if (options.queryKey[1]?.type === "infinite") {
      return queryClient.prefetchInfiniteQuery({
        ...options,
        initialPageParam: undefined,
      } as Parameters<typeof queryClient.prefetchInfiniteQuery>[0]);
    }
    return queryClient.prefetchQuery(options);
  });

  await Promise.allSettled(prefetchPromises);
}

/**
 * Smart prefetch that avoids refetching fresh data
 */
export async function prefetchSmart(
  queryOptions: ReturnType<TRPCQueryOptions<any>>,
  maxAge = 30000,
) {
  const queryClient = getQueryClient();
  const existingData = queryClient.getQueryData(queryOptions.queryKey);
  const queryState = queryClient.getQueryState(queryOptions.queryKey);

  if (
    existingData &&
    queryState?.dataUpdatedAt &&
    Date.now() - queryState.dataUpdatedAt < maxAge
  ) {
    return;
  }

  await queryClient.prefetchQuery(queryOptions);
}

export function HydrateClient(props: Readonly<{ children: React.ReactNode }>) {
  const queryClient = getQueryClient();

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      {props.children}
    </HydrationBoundary>
  );
}
