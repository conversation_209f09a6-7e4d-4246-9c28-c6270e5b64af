import {
  SearchIcon,
  ShieldCheckIcon,
  UserCheckIcon,
  UserIcon,
  UserPlusIcon,
  UsersIcon,
} from "lucide-react";
import Link from "next/link";
import { Suspense } from "react";
import { DataTable } from "@/components/data-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { columns } from "@/components/users/columns";
import { authClient } from "@/lib/auth-client";
import { caller } from "@/lib/trpc";

// User statistics component
async function UserStats() {
  const stats = await caller.adminUsers.getStats();

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardContent className="flex items-center p-6">
          <UsersIcon className="h-8 w-8 text-blue-600" />
          <div className="ml-4">
            <p className="font-medium text-muted-foreground text-sm">
              Total Users
            </p>
            <p className="font-bold text-2xl">{stats.total}</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="flex items-center p-6">
          <ShieldCheckIcon className="h-8 w-8 text-purple-600" />
          <div className="ml-4">
            <p className="font-medium text-muted-foreground text-sm">Admins</p>
            <p className="font-bold text-2xl">{stats.byRole.admin}</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="flex items-center p-6">
          <UserCheckIcon className="h-8 w-8 text-green-600" />
          <div className="ml-4">
            <p className="font-medium text-muted-foreground text-sm">
              Verified
            </p>
            <p className="font-bold text-2xl">{stats.verified}</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="flex items-center p-6">
          <UserIcon className="h-8 w-8 text-orange-600" />
          <div className="ml-4">
            <p className="font-medium text-muted-foreground text-sm">
              2FA Enabled
            </p>
            <p className="font-bold text-2xl">{stats.twoFactorEnabled}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// User stats skeleton
function UserStatsSkeleton() {
  const skeletonItems = ["total", "admins", "verified", "twoFactor"];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {skeletonItems.map((item) => (
        <Card key={item}>
          <CardContent className="flex items-center p-6">
            <Skeleton className="h-8 w-8 rounded" />
            <div className="ml-4 space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-6 w-12" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export default async function UsersPage(params: {
  searchParams: Promise<{ search?: string; role?: string }>;
}) {
  const { search, role } = await params.searchParams;

  const { data } = await authClient.admin.listUsers({
    query: {
      searchField: "email",
      searchValue: search || "",
    },
  });

  const users = data?.users || [];

  // Filter by role if specified
  const filteredUsers = role
    ? users.filter((user) => user.role === role)
    : users;

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header Section */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-2">
          <h1 className="font-bold text-3xl tracking-tight">User Management</h1>
          <p className="text-muted-foreground">
            Manage users, roles, and permissions across your platform
          </p>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/users/invite">
              <UserPlusIcon className="mr-2 h-4 w-4" />
              Invite User
            </Link>
          </Button>
        </div>
      </div>

      {/* User Statistics */}
      <Suspense fallback={<UserStatsSkeleton />}>
        <UserStats />
      </Suspense>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Users ({filteredUsers.length})</CardTitle>
          <CardDescription>
            Search and filter users by role, status, and other criteria
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 md:flex-row md:items-center">
            <div className="relative flex-1">
              <SearchIcon className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users by email..."
                defaultValue={search}
                className="pl-10"
                name="search"
              />
            </div>
            <div className="flex gap-2">
              <Badge variant={!role ? "default" : "secondary"}>
                <Link href="/admin/users">All</Link>
              </Badge>
              <Badge variant={role === "admin" ? "default" : "secondary"}>
                <Link href="/admin/users?role=admin">Admins</Link>
              </Badge>
              <Badge variant={role === "homeowner" ? "default" : "secondary"}>
                <Link href="/admin/users?role=homeowner">Homeowners</Link>
              </Badge>
              <Badge variant={role === "contractor" ? "default" : "secondary"}>
                <Link href="/admin/users?role=contractor">Contractors</Link>
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardContent className="p-0">
          <DataTable columns={columns} data={filteredUsers} />
        </CardContent>
      </Card>
    </div>
  );
}
