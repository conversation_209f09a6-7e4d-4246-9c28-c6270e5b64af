import { format } from "date-fns";
import {
  AlertCircleIcon,
  CalendarIcon,
  ClockIcon,
  DollarSignIcon,
  EditIcon,
  EyeIcon,
  TrashIcon,
  UsersIcon,
} from "lucide-react";
import Link from "next/link";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { caller } from "@/lib/trpc";
import { formatCurrency } from "@/lib/utils";

// Types
type JobWithTasks = {
  tasks?: Array<{
    id: string;
    name: string;
    tradeId: string;
    createdAt: Date;
  }>;
};

// Status color mapping
const getStatusColor = (status: string) => {
  switch (status) {
    case "DRAFT":
      return "secondary";
    case "PUBLISHED":
      return "tc_blue";
    case "AWARDED":
      return "tc_orange";
    case "COMPLETED":
      return "success";
    case "CANCELED":
      return "destructive";
    case "CLOSED":
      return "outline";
    default:
      return "secondary";
  }
};

const getBidStatusColor = (status: string) => {
  switch (status) {
    case "PROPOSED":
      return "tc_light_blue";
    case "ACCEPTED":
      return "success";
    case "REJECTED":
      return "destructive";
    case "CANCELED":
      return "secondary";
    case "WITHDRAWN":
      return "outline";
    default:
      return "secondary";
  }
};

export default async function JobPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const job = await caller.jobs.getById({
    id,
    includeReviews: true,
    includeSchedules: true,
  });

  if (!job) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <Alert variant="destructive">
          <AlertCircleIcon className="h-4 w-4" />
          <AlertDescription>
            Job not found or you don't have permission to view it.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header Section */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <h1 className="font-bold text-3xl tracking-tight">{job.name}</h1>
            <Badge
              variant={
                getStatusColor(job.status) as
                  | "default"
                  | "secondary"
                  | "destructive"
                  | "outline"
                  | "success"
                  | "tc_orange"
                  | "tc_blue"
                  | "tc_light_orange"
                  | "tc_light_blue"
              }
            >
              {job.status}
            </Badge>
          </div>
          <p className="text-muted-foreground">
            Job ID: {job.id} • Created {format(job.createdAt, "PPP")}
          </p>
        </div>

        {/* Admin Actions */}
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/admin/jobs/${job.id}/edit`}>
              <EditIcon className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>
          <Button variant="outline" size="sm">
            <EyeIcon className="mr-2 h-4 w-4" />
            View Public
          </Button>
          <Button variant="destructive" size="sm">
            <TrashIcon className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <DollarSignIcon className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="font-medium text-muted-foreground text-sm">
                Budget
              </p>
              <p className="font-bold text-2xl">{formatCurrency(job.budget)}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center p-6">
            <UsersIcon className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="font-medium text-muted-foreground text-sm">Bids</p>
              <p className="font-bold text-2xl">{job.bids?.length || 0}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center p-6">
            <ClockIcon className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="font-medium text-muted-foreground text-sm">
                Start Date
              </p>
              <p className="font-semibold text-lg">
                {format(job.startsAt, "MMM d, yyyy")}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center p-6">
            <CalendarIcon className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="font-medium text-muted-foreground text-sm">
                Deadline
              </p>
              <p className="font-semibold text-lg">
                {format(job.deadline, "MMM d, yyyy")}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Left Column - Main Content */}
        <div className="space-y-6 lg:col-span-2">
          {/* Job Details Card */}
          <Card>
            <CardHeader>
              <CardTitle>Job Details</CardTitle>
              <CardDescription>Core information about this job</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <div className="font-medium text-muted-foreground text-sm">
                    Job Type
                  </div>
                  <p className="text-sm">{job.jobType}</p>
                </div>
                <div>
                  <div className="font-medium text-muted-foreground text-sm">
                    Recurring
                  </div>
                  <p className="text-sm">
                    {job.isRecurring ? "Yes" : "No"}
                    {job.isRecurring && job.recurringFrequency && (
                      <span className="text-muted-foreground">
                        {" "}
                        ({job.recurringFrequency})
                      </span>
                    )}
                  </p>
                </div>
                <div>
                  <div className="font-medium text-muted-foreground text-sm">
                    Task Bids
                  </div>
                  <p className="text-sm">
                    {job.taskBids ? "Enabled" : "Disabled"}
                  </p>
                </div>
                <div>
                  <div className="font-medium text-muted-foreground text-sm">
                    Last Updated
                  </div>
                  <p className="text-sm">
                    {format(job.updatedAt, "PPP 'at' p")}
                  </p>
                </div>
              </div>

              {job.completedAt && (
                <div>
                  <div className="font-medium text-muted-foreground text-sm">
                    Completed At
                  </div>
                  <p className="text-sm">
                    {format(job.completedAt, "PPP 'at' p")}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tasks Section */}
          <Card>
            <CardHeader>
              <CardTitle>
                Tasks ({(job as JobWithTasks).tasks?.length || 0})
              </CardTitle>
              <CardDescription>Tasks associated with this job</CardDescription>
            </CardHeader>
            <CardContent>
              {(job as JobWithTasks).tasks?.length ? (
                <div className="space-y-3">
                  {(job as JobWithTasks).tasks?.map((task, index) => (
                    <div
                      key={task.id}
                      className="flex items-center justify-between rounded-lg border p-3"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted font-medium text-sm">
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium">{task.name}</p>
                          <p className="text-muted-foreground text-sm">
                            Trade ID: {task.tradeId}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-muted-foreground text-sm">
                          Created {format(task.createdAt, "MMM d, yyyy")}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-muted-foreground">
                  No tasks assigned to this job.
                </p>
              )}
            </CardContent>
          </Card>

          {/* Bids Section */}
          <Card>
            <CardHeader>
              <CardTitle>Bids ({job.bids?.length || 0})</CardTitle>
              <CardDescription>Contractor bids for this job</CardDescription>
            </CardHeader>
            <CardContent>
              {job.bids && job.bids.length > 0 ? (
                <div className="space-y-4">
                  {job.bids.map((bid) => (
                    <div key={bid.id} className="rounded-lg border p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <h4 className="font-semibold">{bid.name}</h4>
                            <Badge
                              variant={
                                getBidStatusColor(bid.status) as
                                  | "default"
                                  | "secondary"
                                  | "destructive"
                                  | "outline"
                                  | "success"
                                  | "tc_orange"
                                  | "tc_blue"
                                  | "tc_light_orange"
                                  | "tc_light_blue"
                              }
                            >
                              {bid.status}
                            </Badge>
                          </div>
                          <p className="text-muted-foreground text-sm">
                            {("organization" in bid &&
                              bid.organization?.name) ||
                              "Unknown Organization"}
                          </p>
                          {bid.description && (
                            <p className="text-sm">{bid.description}</p>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-lg">
                            {formatCurrency(bid.amount)}
                          </p>
                          {bid.estimatedDuration && (
                            <p className="text-muted-foreground text-sm">
                              {bid.estimatedDuration} days
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="mt-3 flex items-center justify-between text-muted-foreground text-sm">
                        <span>
                          Submitted{" "}
                          {format(bid.createdAt, "MMM d, yyyy 'at' h:mm a")}
                        </span>
                        {bid.updatedAt !== bid.createdAt && (
                          <span>
                            Updated{" "}
                            {format(bid.updatedAt, "MMM d, yyyy 'at' h:mm a")}
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-muted-foreground">
                  No bids submitted for this job yet.
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Property Information */}
          <Card>
            <CardHeader>
              <CardTitle>Property Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <div className="font-medium text-muted-foreground text-sm">
                  Property Name
                </div>
                <p className="font-medium text-sm">
                  {job.property?.name || "N/A"}
                </p>
              </div>

              {"address" in job.property && job.property.address && (
                <div>
                  <div className="font-medium text-muted-foreground text-sm">
                    Address
                  </div>
                  <div className="text-sm">
                    <p>{job.property.address.street}</p>
                    <p>
                      {job.property.address.city}, {job.property.address.state}{" "}
                      {job.property.address.zip}
                    </p>
                  </div>
                </div>
              )}

              <div>
                <div className="font-medium text-muted-foreground text-sm">
                  Property ID
                </div>
                <p className="font-mono text-sm">{job.propertyId}</p>
              </div>
            </CardContent>
          </Card>

          {/* Job Status & Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Status Management</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <div className="font-medium text-muted-foreground text-sm">
                  Current Status
                </div>
                <div className="mt-1">
                  <Badge
                    variant={
                      getStatusColor(job.status) as
                        | "default"
                        | "secondary"
                        | "destructive"
                        | "outline"
                        | "success"
                        | "tc_orange"
                        | "tc_blue"
                        | "tc_light_orange"
                        | "tc_light_blue"
                    }
                    className="text-sm"
                  >
                    {job.status}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full">
                  Change Status
                </Button>
                <Button variant="outline" size="sm" className="w-full">
                  Send Notification
                </Button>
                <Button variant="outline" size="sm" className="w-full">
                  Generate Report
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Completion Status */}
          {(job.contractorCompleted || job.homeownerCompleted) && (
            <Card>
              <CardHeader>
                <CardTitle>Completion Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Contractor</span>
                  {job.contractorCompleted ? (
                    <Badge variant="success">Completed</Badge>
                  ) : (
                    <Badge variant="secondary">Pending</Badge>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Homeowner</span>
                  {job.homeownerCompleted ? (
                    <Badge variant="success">Completed</Badge>
                  ) : (
                    <Badge variant="secondary">Pending</Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground text-sm">
                  Total Bids
                </span>
                <span className="font-medium text-sm">
                  {job.bids?.length || 0}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground text-sm">
                  Average Bid
                </span>
                <span className="font-medium text-sm">
                  {job.bids && job.bids.length > 0
                    ? formatCurrency(
                        job.bids.reduce((sum, bid) => sum + bid.amount, 0) /
                          job.bids.length,
                      )
                    : "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground text-sm">
                  Days Since Created
                </span>
                <span className="font-medium text-sm">
                  {Math.floor(
                    (Date.now() - job.createdAt.getTime()) /
                      (1000 * 60 * 60 * 24),
                  )}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
