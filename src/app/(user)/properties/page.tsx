import { Suspense } from "react";
import { PropertyGridSkeleton } from "@/components/loading-states";
import { PageLayout } from "@/components/page-layout";
import { NewProperty } from "@/components/properties/new-property";
import { PropertiesContent } from "@/components/properties/properties-content";
import { getQueryClient, HydrateClient, trpc } from "@/components/trpc/server";

export default async function PropertiesPage() {
  const queryClient = getQueryClient();

  // Prefetch properties data on the server
  await queryClient.prefetchQuery(trpc.properties.list.queryOptions());

  return (
    <PageLayout title="Properties">
      <div className="space-y-6">
        {/* Mobile: Stack vertically, Desktop: Side by side */}
        <div className="flex flex-col gap-4 lg:flex-row lg:gap-6">
          <div className="flex-1">
            <HydrateClient>
              <Suspense fallback={<PropertyGridSkeleton />}>
                <PropertiesContent />
              </Suspense>
            </HydrateClient>
          </div>

          {/* Mobile: Full width button, Desktop: Sidebar */}
          <div className="lg:flex lg:w-80 lg:items-start lg:justify-center">
            <NewProperty />
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
