import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { AcceptBidForm } from "@/components/bid/accept-bid-form";
import { PageLayout } from "@/components/page-layout";
import { getQueryClient, trpc } from "@/components/trpc/server";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { auth } from "@/lib/auth";

export default async function AcceptBidPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const session = await auth.api.getSession({ headers: await headers() });
  const user = session?.user;

  if (!user) {
    redirect("/sign-in");
  }

  // Verify user is a homeowner
  if (user.role !== "homeowner") {
    redirect("/dashboard");
  }

  const queryClient = getQueryClient();
  const bid = await queryClient.fetchQuery(
    trpc.bids.getById.queryOptions({ id }),
  );

  if (!bid || bid.status !== "PROPOSED") {
    redirect(`/bids/${id}`);
  }

  return (
    <PageLayout title="Accept Bid">
      <div className="p-8">
        <div className="mx-auto max-w-2xl">
          <Card>
            <CardHeader>
              <CardTitle>Confirm Bid Acceptance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-6 space-y-2">
                <p>You are about to accept the following bid:</p>
                <p className="font-medium">{bid.name}</p>
                <p>Amount: ${bid.amount}</p>
                <p>Contractor: {bid.organization.name}</p>
                <p>Project: {bid.job.name}</p>
              </div>
              <AcceptBidForm bidId={id} />
            </CardContent>
          </Card>
        </div>
      </div>
    </PageLayout>
  );
}
