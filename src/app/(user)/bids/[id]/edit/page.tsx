import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { BidForm } from "@/components/bid/bid-form";
import { PageLayout } from "@/components/page-layout";
import { getQueryClient, trpc } from "@/components/trpc/server";
import { auth } from "@/lib/auth";

export default async function EditBidPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const session = await auth.api.getSession({ headers: await headers() });
  const user = session?.user;

  if (!user) {
    redirect("/sign-in");
  }

  // Verify user is a professional
  if (user.role !== "contractor") {
    redirect("/dashboard");
  }

  const queryClient = getQueryClient();
  const bid = await queryClient.fetchQuery(
    trpc.bids.getById.queryOptions({ id }),
  );

  if (!bid || bid.status !== "PROPOSED") {
    redirect(`/bids/${id}`);
  }

  return (
    <PageLayout title="Edit Bid">
      <div className="p-8">
        <BidForm
          jobId={bid.job.id}
          initialData={bid}
          bidId={id}
          isEditing={true}
        />
      </div>
    </PageLayout>
  );
}
