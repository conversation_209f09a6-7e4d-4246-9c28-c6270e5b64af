import { format } from "date-fns";
import {
  BuildingIcon,
  CalendarIcon,
  ClockIcon,
  DollarSignIcon,
  MailIcon,
  MapPinIcon,
  PhoneIcon,
} from "lucide-react";
import { headers } from "next/headers";
import Link from "next/link";
import { redirect } from "next/navigation";
import { PusherChat } from "@/components/chat";
import { PageLayout } from "@/components/page-layout";
import { HomeownerBidFlowTour } from "@/components/tours/homeowner-bid-flow-tour";
import { getQueryClient, trpc } from "@/components/trpc/server";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { auth } from "@/lib/auth";
import { BID_STATUS_VARIANTS, getStatusVariant } from "@/lib/utils";

export default async function BidDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const session = await auth.api.getSession({ headers: await headers() });
  const user = session?.user;

  if (!user) {
    redirect("/sign-in");
  }

  const queryClient = getQueryClient();
  const bid = await queryClient.fetchQuery(
    trpc.bids.getById.queryOptions({ id }),
  );

  if (!bid) {
    redirect("/dashboard");
  }

  const isHomeowner = user.role === "homeowner";

  const actions = (
    <div className="flex gap-2">
      {isHomeowner && bid.status !== "ACCEPTED" && (
        <Button variant="tc_blue" asChild>
          <Link href={`/bids/${bid.id}/accept`}>Accept Bid</Link>
        </Button>
      )}
      {!isHomeowner && bid.status === "PROPOSED" && (
        <>
          <Button variant="tc_blue" asChild>
            <Link href={`/bids/${bid.id}/edit`}>Edit Bid</Link>
          </Button>
          <Button className="bg-red-600 hover:bg-red-700" asChild>
            <Link href={`/bids/${bid.id}/withdraw`}>Withdraw Bid</Link>
          </Button>
        </>
      )}
      <Button variant="tc_orange" asChild>
        <Link href={`/jobs/${bid.job.id}`}>Back to Project</Link>
      </Button>
    </div>
  );

  return (
    <PageLayout title={`Bid: ${bid.name}`} actions={actions}>
      {isHomeowner && bid.status === "PROPOSED" && <HomeownerBidFlowTour />}
      <div className="p-8">
        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Bid Details</CardTitle>
                  <Badge
                    variant={getStatusVariant(bid.status, BID_STATUS_VARIANTS)}
                  >
                    {bid.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="bid-details">
                <div className="space-y-6">
                  <div>
                    <h3 className="font-medium">Project</h3>
                    <p className="text-muted-foreground">
                      {bid.job.name} at {bid.job.property.name}
                    </p>
                  </div>

                  <div className="flex flex-wrap gap-6">
                    <div className="flex items-center gap-2">
                      <DollarSignIcon className="h-4 w-4 text-orange-500" />
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Bid Amount
                        </p>
                        <p className="font-medium">${bid.amount}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <ClockIcon className="h-4 w-4 text-orange-500" />
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Estimated Duration
                        </p>
                        <p className="font-medium">
                          {bid.estimatedDuration} days
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <CalendarIcon className="h-4 w-4 text-orange-500" />
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Submitted On
                        </p>
                        <p className="font-medium">
                          {format(bid.createdAt, "PPP")}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="mb-2 font-medium">Bid Description</h3>
                    <div className="rounded-md bg-muted/50 p-4">
                      <p className="whitespace-pre-wrap">{bid.description}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>
                  <div className="flex items-center justify-between">
                    <span>Contractor Information</span>
                    <Link
                      href={`/contractors/${bid.organization.id}`}
                      className="text-blue-600 text-sm hover:underline"
                    >
                      View Profile
                    </Link>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="contractor-info">
                <div className="mt-4 space-y-4">
                  <div>
                    <h3 className="flex items-center gap-2 font-medium">
                      <BuildingIcon className="h-4 w-4 text-orange-500" />
                      Organization
                    </h3>
                    <p>{bid.organization.name}</p>
                    {bid.organization.trade && (
                      <Badge variant="outline" className="mt-1">
                        {bid.organization.trade.name}
                      </Badge>
                    )}
                  </div>

                  {/* Always show the View Profile link regardless of bid status */}
                  <Link
                    href={`/contractors/${bid.organization.id}`}
                    className="inline-flex items-center text-blue-600 text-sm hover:underline"
                  >
                    View Full Profile
                  </Link>

                  {typeof bid.distance === "number" && (
                    <div>
                      <h3 className="flex items-center gap-2 font-medium">
                        <MapPinIcon className="h-4 w-4 text-orange-500" />
                        Distance
                      </h3>
                      <p>{Number(bid.distance).toFixed(1)} miles away</p>
                    </div>
                  )}

                  {bid.organization.address && (
                    <div>
                      <h3 className="flex items-center gap-2 font-medium">
                        <MapPinIcon className="h-4 w-4 text-orange-500" />
                        Address
                      </h3>
                      <p>
                        {bid.organization.address.street},{" "}
                        {bid.organization.address.city},{" "}
                        {bid.organization.address.state}{" "}
                        {bid.organization.address.zip}
                      </p>
                    </div>
                  )}

                  {bid.organization.phone && (
                    <div>
                      <h3 className="flex items-center gap-2 font-medium">
                        <PhoneIcon className="h-4 w-4 text-orange-500" />
                        Phone
                      </h3>
                      <p>{bid.organization.phone}</p>
                    </div>
                  )}

                  {bid.organization.email && (
                    <div>
                      <h3 className="flex items-center gap-2 font-medium">
                        <MailIcon className="h-4 w-4 text-orange-500" />
                        Email
                      </h3>
                      <p>{bid.organization.email}</p>
                    </div>
                  )}

                  {!bid.organization.phone &&
                    !bid.organization.email &&
                    !bid.organization.address && (
                      <div className="rounded-md bg-muted/50 p-4 text-muted-foreground text-sm">
                        <p>
                          No contact information available for this contractor.
                        </p>
                      </div>
                    )}
                </div>

                <div className="mt-6">
                  {isHomeowner && bid.status !== "ACCEPTED" && (
                    <Button className="mb-2 w-full" variant="tc_blue" asChild>
                      <Link href={`/bids/${bid.id}/accept`}>Accept Bid</Link>
                    </Button>
                  )}

                  {!isHomeowner && bid.status === "PROPOSED" && (
                    <>
                      <Button className="mb-2 w-full" variant="tc_blue" asChild>
                        <Link href={`/bids/${bid.id}/edit`}>Edit Bid</Link>
                      </Button>
                      <Button
                        className="mb-2 w-full bg-red-600 hover:bg-red-700"
                        asChild
                      >
                        <Link href={`/bids/${bid.id}/withdraw`}>
                          Withdraw Bid
                        </Link>
                      </Button>
                    </>
                  )}

                  <Button variant="tc_orange" className="w-full" asChild>
                    <Link href={`/jobs/${bid.job.id}`}>Back to Project</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      {/** biome-ignore lint/nursery/useUniqueElementIds: Id is unique for tour */}
      <div className="mx-8 mb-4" id="chat">
        <Card>
          <CardHeader>
            <CardTitle>Messages</CardTitle>
          </CardHeader>
          <CardContent>
            <PusherChat bidId={bid.id} userId={user.id} />
          </CardContent>
        </Card>
      </div>
    </PageLayout>
  );
}
