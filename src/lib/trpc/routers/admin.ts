import { count, eq, inArray } from "drizzle-orm";
import { db } from "@/db";
import { account, job } from "@/db/schema";
import { calculateJobsTrend, calculateUsersTrend } from "@/lib/utils";
import { protectedProcedure, router } from "../procedures";

export const adminRouter = router({
  getStats: protectedProcedure.query(async () => {
    // Get total users
    const totalUsers = await db.$count(account);

    // Get jobs by status
    const jobsByStatus = await db
      .select({ count: count(job.id), status: job.status })
      .from(job)
      .groupBy(job.status);

    // Get active and completed jobs
    const activeJobs = await db.$count(job, eq(job.status, "PUBLISHED"));

    const completedJobs = await db.$count(
      job,
      inArray(job.status, ["AWARDED", "CLOSED"]),
    );

    // Calculate trends
    const usersTrend = await calculateUsersTrend(db);
    const jobsTrend = await calculateJobsTrend(db);
    const completedJobsTrend = await calculateJobsTrend(db, [
      "AWARDED",
      "CLOSED",
    ]);

    // Calculate revenue (placeholder - implement actual calculation)
    const totalRevenue = 25000;
    const revenueTrend = 8; // Example value - implement actual calculation

    // Example data for charts
    const jobStatusData = jobsByStatus.map((item) => ({
      name: item.status,
      value: item.count,
    }));

    const revenueByMonth = [
      { month: "Jan", value: 4000 },
      { month: "Feb", value: 3000 },
      { month: "Mar", value: 5000 },
      { month: "Apr", value: 7000 },
      { month: "May", value: 6000 },
      { month: "Jun", value: 8000 },
    ];

    return {
      totalUsers,
      activeJobs,
      completedJobs,
      totalRevenue,
      usersTrend,
      jobsTrend,
      completedJobsTrend,
      revenueTrend,
      jobsByStatus: jobStatusData,
      revenueByMonth,
    };
  }),
});
