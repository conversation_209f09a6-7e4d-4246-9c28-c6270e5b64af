import { router } from "@/lib/trpc/procedures";
import { completionRouter } from "./completion";
import { coreJobsRouter } from "./core";
import { creationRouter } from "./creation";
import { listingRouter } from "./listing";
import { quickHireRouter } from "./quickhire";
import { scheduleRouter } from "./schedule";

export const jobsRouter = router({
  ...completionRouter,
  ...coreJobsRouter,
  ...creationRouter,
  ...listingRouter,
  ...quickHireRouter,
  ...scheduleRouter,
});
