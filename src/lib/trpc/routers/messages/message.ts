import { TRPCError } from "@trpc/server";
import { and, asc, eq, type InferSelectModel } from "drizzle-orm";
import z from "zod";
import { db } from "@/db";
import {
  account,
  bid,
  chat,
  job,
  membership,
  message,
  property,
} from "@/db/schema";
import { processChatCommand } from "@/lib/chat-commands";
import { triggerChatEvent } from "@/lib/pusher-server";
import { protectedProcedure } from "@/lib/trpc/procedures";
import {
  findOrCreateChat,
  getBidWithRelations,
  getJobWithRelations,
} from "@/lib/trpc/utils/entities";
import {
  checkJobOwnership,
  checkOrganizationMembership,
  checkPropertyOwnership,
  requireAuth,
} from "@/lib/trpc/utils/permissions";

export const messageRouter = {
  listMessages: protectedProcedure
    .input(
      z
        .object({
          bidId: z.string().optional(),
          jobId: z.string().optional(),
        })
        .refine((data) => !!data.bidId || !!data.jobId, {
          message: "Either bidId or jobId must be provided",
        }),
    )
    .query(async ({ input, ctx }) => {
      let c: InferSelectModel<typeof chat> | null | undefined;

      // Determine which type of chat we're dealing with
      if (input.bidId) {
        const bid = await getBidWithRelations(input.bidId);

        // Check permissions
        const isPropertyOwner = await checkJobOwnership(ctx.userId, bid.jobId);
        const isBidder = await checkOrganizationMembership(
          ctx.userId,
          bid.organizationId,
        );

        requireAuth(
          isPropertyOwner || isBidder,
          "You don't have permission to access this chat",
        );

        c = await findOrCreateChat({ bidId: input.bidId });
      } else if (input.jobId) {
        const job = await getJobWithRelations(input.jobId);

        // Check permissions
        const isPropertyOwner = await checkPropertyOwnership(
          ctx.userId,
          job.propertyId,
        );
        const isProfessional = ctx.role === "contractor";

        requireAuth(
          isPropertyOwner || !!isProfessional,
          "You don't have permission to access this chat",
        );

        c = await findOrCreateChat({ jobId: input.jobId });
      }

      if (!c) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Chat not found",
        });
      }

      // Fetch messages
      const messageList = await db.query.message.findMany({
        where: eq(message.chatId, c.id),
        orderBy: [asc(message.createdAt)],
      });

      return messageList;
    }),

  createMessage: protectedProcedure
    .input(
      z
        .object({
          bidId: z.string().optional(),
          jobId: z.string().optional(),
          content: z.string().min(1),
          senderInitials: z.string().optional(),
          senderAvatarUrl: z.string().optional(),
        })
        .refine((data) => !!data.bidId || !!data.jobId, {
          message: "Either bidId or jobId must be provided",
        }),
    )
    .mutation(async ({ input, ctx }) => {
      let c: InferSelectModel<typeof chat> | null | undefined;
      let senderType: "homeowner" | "professional" = "homeowner";
      const bidId = input.bidId;
      const jobId = input.jobId;
      let content = input.content;
      let commandResponse = null;

      // Check if message is a command (starts with /)
      if (content.startsWith("/")) {
        const commandResult = await processChatCommand({
          command: content,
          userId: ctx.userId,
          bidId,
          jobId,
        });

        if (commandResult.success) {
          // If command was processed successfully, update the content
          content = `[Command executed: ${commandResult.message}]`;
          commandResponse = commandResult.data;
        } else {
          // If command failed, show error message
          content = `[Command failed: ${commandResult.message}]`;
        }
      }

      if (input.bidId) {
        // Bid-related message
        const b = await db.query.bid.findFirst({
          where: eq(bid.id, input.bidId),
          with: {
            job: {
              with: {
                property: true,
              },
            },
            organization: true,
          },
        });

        if (!b) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Bid not found",
          });
        }

        // Check permissions
        const isPropertyOwner = await db.query.account.findFirst({
          where: and(
            eq(account.userId, ctx.userId),
            eq(property.id, b.job.propertyId),
          ),
        });

        const isBidder = await db.query.membership.findFirst({
          where: and(
            eq(membership.userId, ctx.userId),
            eq(membership.organizationId, b.organizationId),
          ),
        });

        if (!isPropertyOwner && !isBidder) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "You don't have permission to send messages",
          });
        }

        // Find or create chat
        c = await db.query.chat.findFirst({
          where: eq(chat.bidId, input.bidId),
        });

        if (!c) {
          [c] = await db
            .insert(chat)
            .values({ bidId: input.bidId })
            .returning();
        }

        // Determine sender type
        senderType = isPropertyOwner ? "homeowner" : "professional";
      } else if (input.jobId) {
        // Job-related message
        const j = await db.query.job.findFirst({
          where: eq(job.id, input.jobId),
          with: {
            property: true,
          },
        });

        if (!j) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Job not found",
          });
        }

        // Check permissions
        const isPropertyOwner = await db.query.account.findFirst({
          where: and(
            eq(account.userId, ctx.userId),
            eq(property.id, j.propertyId),
          ),
        });

        const isProfessional = ctx.role === "contractor";

        if (!isPropertyOwner && !isProfessional) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "You don't have permission to send messages",
          });
        }

        // Find or create chat
        c = await db.query.chat.findFirst({
          where: eq(chat.jobId, input.jobId),
        });

        if (!c) {
          [c] = await db
            .insert(chat)
            .values({ jobId: input.jobId })
            .returning();
        }

        // Determine sender type
        senderType = isPropertyOwner ? "homeowner" : "professional";
      }

      if (!c) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create or find chat",
        });
      }

      // Create message
      const [newMessage] = await db
        .insert(message)
        .values({
          chatId: c.id,
          content: content,
          senderId: ctx.userId,
          senderType,
          senderInitials: input.senderInitials,
          senderAvatarUrl: input.senderAvatarUrl,
          isCommand: content.startsWith("[Command"),
          commandData: commandResponse ? JSON.stringify(commandResponse) : null,
        })
        .returning();

      if (!newMessage) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create message",
        });
      }

      // Trigger Pusher event for real-time message delivery
      try {
        await triggerChatEvent(c.id, "message", newMessage);
      } catch (error) {
        console.error("Failed to trigger Pusher event:", error);
        // Don't fail the message creation if Pusher fails
      }

      return message;
    }),
};
