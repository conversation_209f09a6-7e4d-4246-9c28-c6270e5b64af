/** biome-ignore-all lint/suspicious/noExplicitAny: Type of options is unknown */
import { and, asc, desc, eq, inArray, sql } from "drizzle-orm";
import type { PgColumn, PgTable } from "drizzle-orm/pg-core";
import type { Db } from "@/db";
import {
  account,
  address,
  bid,
  job,
  membership,
  organization,
  property,
  review,
  schedule,
  trade,
} from "@/db/schema";

/**
 * Query Optimization Utilities
 *
 * This module provides optimized query patterns to replace inefficient queries
 * found in the tRPC routers. It focuses on:
 * 1. Eliminating N+1 queries
 * 2. Batch loading related data
 * 3. Optimizing complex joins
 * 4. Providing reusable query builders
 */

// ============================================================================
// BATCH QUERY UTILITIES
// ============================================================================

/**
 * Batch load jobs with all related data in a single query
 * Replaces multiple separate queries in jobs router
 */
export async function batchLoadJobsWithRelations(
  db: Db,
  jobIds: string[],
  options: {
    includeBids?: boolean;
    includeProperty?: boolean;
    includeSchedules?: boolean;
    includeReviews?: boolean;
  } = {},
) {
  if (jobIds.length === 0) return [];

  const {
    includeBids = true,
    includeProperty = true,
    includeSchedules = false,
    includeReviews = false,
  } = options;

  return db.query.job.findMany({
    where: inArray(job.id, jobIds),
    with: {
      tasks: true,
      ...(includeProperty && {
        property: {
          with: {
            address: true,
          },
        },
      }),
      ...(includeBids && {
        bids: {
          with: {
            organization: {
              with: {
                trade: true,
                address: true,
                memberships: {
                  columns: {
                    userId: true,
                  },
                },
              },
            },
          },
        },
      }),
      ...(includeSchedules && {
        schedules: true,
      }),
      ...(includeReviews && {
        reviews: true,
      }),
    },
    extras: {
      bidsCount: db.$count(bid, eq(bid.jobId, job.id)).as("bidsCount"),
    },
  });
}

/**
 * Batch load organizations with member counts and trade info
 * Optimizes contractor search and listing queries
 */
export async function batchLoadOrganizationsWithStats(
  db: Db,
  organizationIds: string[],
) {
  if (organizationIds.length === 0) return [];

  return db.query.organization.findMany({
    where: inArray(organization.id, organizationIds),
    with: {
      trade: true,
      address: true,
      memberships: {
        with: {
          user: {
            columns: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
    extras: {
      memberCount: db
        .$count(membership, eq(membership.organizationId, organization.id))
        .as("memberCount"),
      activeJobsCount: db
        .$count(
          job,
          and(
            eq(bid.organizationId, organization.id),
            eq(bid.status, "ACCEPTED"),
            eq(job.status, "PUBLISHED"),
          ),
        )
        .as("activeJobsCount"),
    },
  });
}

// ============================================================================
// OPTIMIZED QUERY BUILDERS
// ============================================================================

/**
 * Optimized query for user's jobs with property and bid information
 * Replaces inefficient subqueries in jobs/listing.ts
 */
export function buildUserJobsQuery(db: Db, userId: string) {
  return db
    .select({
      job,
      property,
      address,
      bidsCount: sql<number>`count(${bid.id})`.as("bidsCount"),
      acceptedBid: sql<any>`
        json_agg(
          json_build_object(
            'id', ${bid.id},
            'amount', ${bid.amount},
            'status', ${bid.status},
            'organization', json_build_object(
              'id', ${organization.id},
              'name', ${organization.name}
            )
          )
        ) FILTER (WHERE ${bid.status} = 'ACCEPTED')
      `.as("acceptedBid"),
    })
    .from(job)
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .leftJoin(organization, eq(bid.organizationId, organization.id))
    .where(eq(property.userId, userId))
    .groupBy(job.id, property.id, address.id)
    .orderBy(desc(job.createdAt));
}

/**
 * Optimized query for organization's active jobs
 * Eliminates multiple queries in contractor stats
 */
export function buildOrganizationActiveJobsQuery(
  db: Db,
  organizationId: string,
) {
  return db
    .select({
      job,
      property,
      address,
      bid,
      scheduleInfo: sql<any>`
        json_agg(
          json_build_object(
            'id', ${schedule.id},
            'proposedStartDate', ${schedule.proposedStartDate},
            'proposedEndDate', ${schedule.proposedEndDate},
            'status', ${schedule.status}
          )
        ) FILTER (WHERE ${schedule.id} IS NOT NULL)
      `.as("scheduleInfo"),
    })
    .from(job)
    .innerJoin(
      bid,
      and(
        eq(job.id, bid.jobId),
        eq(bid.organizationId, organizationId),
        eq(bid.status, "ACCEPTED"),
      ),
    )
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(schedule, eq(job.id, schedule.jobId))
    .where(eq(job.status, "PUBLISHED"))
    .groupBy(job.id, property.id, address.id, bid.id)
    .orderBy(desc(job.createdAt));
}

// ============================================================================
// SEARCH OPTIMIZATION
// ============================================================================

/**
 * Optimized contractor search with full-text search capabilities
 * Replaces ILIKE queries with proper text search
 */
export function buildContractorSearchQuery(
  db: Db,
  searchQuery: string,
  excludeIds: string[] = [],
  limit = 10,
) {
  const searchConditions = [];

  // Use proper text search instead of ILIKE for better performance
  if (searchQuery.trim()) {
    const tsQuery = searchQuery.trim().split(" ").join(" & ");
    searchConditions.push(
      sql`(
        to_tsvector('english', ${organization.name}) @@ to_tsquery('english', ${tsQuery}) OR
        to_tsvector('english', ${trade.name}) @@ to_tsquery('english', ${tsQuery}) OR
        to_tsvector('english', coalesce(${organization.description}, '')) @@ to_tsquery('english', ${tsQuery})
      )`,
    );
  }

  if (excludeIds.length > 0) {
    searchConditions.push(
      sql`${organization.id} NOT IN (${sql.join(
        excludeIds.map((id) => sql`${id}`),
        sql`, `,
      )})`,
    );
  }

  return db
    .select({
      organization,
      trade,
      address,
      memberCount: sql<number>`count(${membership.userId})`.as("memberCount"),
      avgRating: sql<number>`avg(${review.rating})`.as("avgRating"),
      reviewCount: sql<number>`count(${review.id})`.as("reviewCount"),
    })
    .from(organization)
    .leftJoin(trade, eq(organization.tradeId, trade.id))
    .leftJoin(address, eq(organization.addressId, address.id))
    .leftJoin(membership, eq(organization.id, membership.organizationId))
    .leftJoin(bid, eq(organization.id, bid.organizationId))
    .leftJoin(job, and(eq(bid.jobId, job.id), eq(job.status, "COMPLETED")))
    .leftJoin(
      review,
      and(eq(review.jobId, job.id), eq(review.reviewType, "CONTRACTOR_REVIEW")),
    )
    .where(searchConditions.length > 0 ? and(...searchConditions) : undefined)
    .groupBy(organization.id, trade.id, address.id)
    .orderBy(
      desc(sql`avg(${review.rating})`),
      desc(sql`count(${review.id})`),
      asc(organization.name),
    )
    .limit(limit);
}

// ============================================================================
// STATISTICS OPTIMIZATION
// ============================================================================

/**
 * Optimized admin statistics query
 * Combines multiple count queries into a single query
 */
export async function getOptimizedAdminStats(db: Db) {
  const [stats] = await db
    .select({
      totalUsers: sql<number>`count(distinct ${account.id})`.as("totalUsers"),
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      publishedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED')`.as(
          "publishedJobs",
        ),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} in ('AWARDED', 'CLOSED'))`.as(
          "completedJobs",
        ),
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      acceptedBids:
        sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')`.as(
          "acceptedBids",
        ),
      totalOrganizations: sql<number>`count(distinct ${organization.id})`.as(
        "totalOrganizations",
      ),
    })
    .from(account)
    .fullJoin(job, sql`true`)
    .fullJoin(bid, sql`true`)
    .fullJoin(organization, sql`true`);

  return stats;
}

/**
 * Optimized user statistics query
 * Replaces multiple separate queries in accounts router
 */
export async function getOptimizedUserStats(db: Db, userId: string) {
  const [stats] = await db
    .select({
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      activeJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED')`.as(
          "activeJobs",
        ),
      completedJobs:
        sql<number>`count(distinct ${job.id}) filter (where ${job.status} in ('AWARDED', 'CLOSED'))`.as(
          "completedJobs",
        ),
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      avgJobBudget: sql<number>`avg(${job.budget})`.as("avgJobBudget"),
    })
    .from(property)
    .leftJoin(job, eq(property.id, job.propertyId))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .where(eq(property.userId, userId));

  return stats;
}

// ============================================================================
// PAGINATION UTILITIES
// ============================================================================

/**
 * Cursor-based pagination builder for large datasets
 */
export function buildCursorPaginationQuery<T extends PgTable>(
  baseQuery: any,
  cursorColumn: PgColumn,
  cursor?: string | Date,
  limit = 20,
  direction: "asc" | "desc" = "desc",
) {
  let query = baseQuery;

  if (cursor) {
    const operator = direction === "desc" ? sql`<` : sql`>`;
    query = query.where(sql`${cursorColumn} ${operator} ${cursor}`);
  }

  query = query
    .orderBy(direction === "desc" ? desc(cursorColumn) : asc(cursorColumn))
    .limit(limit + 1); // +1 to check if there's a next page

  return query;
}
